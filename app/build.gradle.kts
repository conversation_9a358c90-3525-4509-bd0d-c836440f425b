import java.io.FileInputStream
import java.io.FileOutputStream
import java.io.InputStream
import java.io.OutputStream
import java.net.URL
import java.util.Properties

plugins {
    alias(libs.plugins.android.application)
    alias(libs.plugins.kotlin.android)
//    alias(libs.plugins.dagger.hilt)
    alias(libs.plugins.devtools.ksp)
    alias(libs.plugins.compose.compiler)
    alias(libs.plugins.kotlin.serialization)
    id("kotlin-parcelize")
    id("com.google.gms.google-services")
    // Add the Crashlytics Gradle plugin
    id("com.google.firebase.crashlytics")
    id("com.google.firebase.firebase-perf")
}
// get the keystore properties
val keystorePropertiesFile = rootProject.file("keystore.properties")
val keystoreProperties = Properties()
keystoreProperties.load(FileInputStream(keystorePropertiesFile))


android {
    namespace = "org.aihealth.ineck"
    compileSdk = libs.versions.compileSdk.get().toInt()

    defaultConfig {
        applicationId = "org.aihealth.ineck"
        minSdk = libs.versions.minSdk.get().toInt()
        targetSdk = libs.versions.targetSdk.get().toInt()
        versionCode = 191
        versionName = "1.23.3"
//      无会员：v1.5.8 会员：v1.6.7
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables.useSupportLibrary = true

        // auth0
        manifestPlaceholders["auth0Domain"] = "@string/com_auth0_domain"
        manifestPlaceholders["auth0Scheme"] = "@string/com_auth0_scheme"

        // 设置支持的SO库架构·
        ndk {
            abiFilters.add("arm64-v8a")
            abiFilters.add("armeabi-v7a")
        }
    }
    signingConfigs {
        create("release") {
            keyAlias = keystoreProperties["keyAlias"] as String
            keyPassword = keystoreProperties["keyPassword"] as String
            storeFile = file(keystoreProperties["storeFile"] as String)
            storePassword = keystoreProperties["storePassword"] as String
        }
    }
    sourceSets {
        getByName("main").java.srcDirs("src/main/java")
        getByName("main").assets.srcDir("src/main/assets")
    }
    buildTypes {
        getByName("release") {
            //打包是否混淆
            isMinifyEnabled = false
            //是否支持调试
            isDebuggable = false
            //是否删除无用的文件
            isShrinkResources = false
            // 混淆文件
            proguardFiles(getDefaultProguardFile("proguard-android-optimize.txt"), "proguard-rules.pro")
            // 签名配置
            signingConfig = signingConfigs.getByName("release")
        }
        getByName("debug") {
            //是否支持调试
            isDebuggable = true
            signingConfig = signingConfigs.getByName("release")
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
        freeCompilerArgs += "-Xjvm-default=all"
    }

    buildFeatures{
        compose = true
        buildConfig = true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.7"
    }
    packaging {
        resources.excludes.add("/META-INF/{AL2.0,LGPL2.1}")
        resources.excludes.add("/META-INF/DEPENDENCIES")
        resources.excludes.add("/META-INF/LICENSE")
        resources.excludes.add("/META-INF/LICENSE.txt")
        resources.excludes.add("/META-INF/NOTICE")
        resources.excludes.add("/META-INF/NOTICE.txt")
    }
    splits.abi {
        include("armeabi-v7a", "arm64-v8a")
    }
    androidResources {
        generateLocaleConfig = true
    }
    buildToolsVersion = "35.0.0"
    flavorDimensions += listOf("store")
    productFlavors {
        create("googlePlay") {
            dimension = "store"
        }
        create("chinaStore") {
            dimension = "store"
        }
    }
    
    // 添加lint配置，禁用导致错误的检查器
    lint {
        disable += "NullSafeMutableLiveData"
    }
}

dependencies {
    // compose
    implementation(platform(libs.compose.bom))
    implementation(libs.bundles.compose)

    // navigation
    implementation(libs.navigation.compose)
    implementation(libs.lifecycle.runtime.compose)

    // Android
    implementation(libs.lifecycle.common)
    implementation(libs.activity.compose)
    implementation(libs.core.ktx)
    implementation(libs.work.runtime.ktx)
    implementation(libs.lifecycle.runtime.ktx)
    implementation(libs.lifecycle.viewmodel.compose)
    implementation(libs.lifecycle.viewmodel.ktx)
    implementation(libs.lifecycle.service)

    // accompanist
    implementation(libs.accompanist.systemuicontroller)
    implementation(libs.accompanist.pager)
    implementation(libs.accompanist.flowlayout)
    implementation(libs.accompanist.navigation.material)
    implementation(libs.accompanist.navigation.animation)
    implementation(libs.accompanist.webview)

    // 网络图片加载框架
    implementation(libs.coil.compose)

    // ConstraintLayout 约束布局
    implementation(libs.constraintlayout.compose)

    // Compose pager
    implementation(libs.compose.pager)

    // Gson
    implementation(libs.gson)

    // Retrofit
    implementation(libs.retrofit)
    implementation(libs.retrofit.converter.gson)
    implementation(libs.logging.interceptor)

    // authing
    implementation(libs.authing.core)

    // auth0
    implementation(libs.auth0)

    // wechat login and pay
    implementation(libs.wechat)
    implementation(libs.wechat.pay)
    //kotlin序列化
    //https://kotlinlang.org/docs/serialization.html
    implementation(libs.kotlinx.serialization.json)

    // 二维码识别
    implementation(libs.qrcode)

    // camera
    implementation(libs.camera.lifecycle)
    implementation(libs.camera.view)
    implementation(libs.camera)

    // Room
    implementation(libs.room)
    implementation(libs.zoomvideosdk.core)
    implementation(libs.runtime.android)
    implementation(libs.accompanist.swiperefresh)
    implementation(libs.appcompat)
    implementation(libs.material)
    implementation(libs.activity)
    releaseImplementation(libs.library.no.op)
    debugImplementation(libs.library)
    ksp(libs.room.compiler)
    implementation(libs.room.ktx)

    // DataStore
    implementation(libs.datastore.preferences)
    implementation(libs.datastore.preferences.core)

    // Lottie
    implementation(libs.lottie)

    // MLKit
    implementation(libs.face.detection)
    implementation(libs.vision.common)

    // Pose Land marker
    implementation(libs.pose.landmarker)

    // Google Services Login
    implementation(libs.google.services.login)

    //  Bluetooth LE.
    implementation(libs.nordic.semi.dfu)

    //facebook login
//    implementation(libs.facebook.sdk)

    // exoplayer
    implementation(libs.bundles.media3)

    // to fix the HuaWei crash problem
    implementation(libs.bundles.cronet)

    // Use to implement health connects
    implementation(libs.connect.client)

    // jwt decode
    implementation(libs.jwtdecode)
    implementation(libs.timber)
    // hilt
//    implementation(libs.hilt.android)
//    ksp(libs.hilt.compiler)
//    implementation(libs.hilt.navigation.compose)

    // kotlin-parcelize
    implementation(libs.kotlin.parcelize.runtime)
    implementation(libs.kotlin.android.extensions.runtime)
    implementation(libs.threetenabp)

    // XXPermissions
    implementation(libs.xxPermissions)
    implementation(libs.installreferrer)

    // Import the BoM for the Firebase platform
    implementation(platform(libs.firebase.bom))

    // Add the dependencies for the Crashlytics and Analytics libraries
    // When using the BoM, you don")t specify versions in Firebase library dependencies
    implementation(libs.firebase.crashlytics)
    implementation(libs.firebase.crashlytics.ndk)
    implementation(libs.firebase.analytics)
    implementation(libs.firebase.messaging)
    implementation(libs.firebase.pref)
    implementation(libs.billing)
    implementation(libs.billing.ktx)
    implementation (libs.hbrecorder)
    // 友盟统计SDK
    implementation ("com.umeng.umsdk:common:+") // (必选)版本号
    implementation ("com.umeng.umsdk:asms:+") // asms包依赖(必选)

    //友盟Push依赖（必须）
    implementation ("com.umeng.umsdk:push:+")
    // OPPO推送相关依赖
    api ("com.umeng.umsdk:oppo-umengaccs:+")
    api ("com.umeng.umsdk:oppo-push:+")
    api ("com.umeng.umsdk:xiaomi-umengaccs:2.2.0")
    api ("com.umeng.umsdk:xiaomi-push:6.0.1")

    // Socket.IO 客户端
    implementation("io.socket:socket.io-client:2.1.0") {
        // 排除旧版本的okhttp依赖
        exclude(group = "okhttp3", module = "okhttp")
    }
    
    // test
    testImplementation(libs.junit)
    androidTestImplementation(libs.ext.junit)
    androidTestImplementation(libs.espresso.core)
    androidTestImplementation(platform(libs.compose.bom))
    androidTestImplementation(libs.compose.ui.test.junit)
    debugImplementation(libs.compose.ui.tooling)
    debugImplementation(libs.compose.ui.test.manifest)


}

tasks.register("downloadFile") {
    doLast {
        val url =
            URL("https://storage.googleapis.com/mediapipe-models/pose_landmarker/pose_landmarker_lite/float16/1/pose_landmarker_lite.task")
        val connection = url.openConnection()
        val inputStream: InputStream = connection.getInputStream()
        val file = File("app/src/main/assets/pose_landmarker_lite.task")

        // 确保目标目录存在
        file.parentFile.mkdirs()

        val outputStream: OutputStream = FileOutputStream(file)
        inputStream.copyTo(outputStream)

        inputStream.close()
        outputStream.close()
    }
}
//tasks.named("preBuild") {
//    dependsOn("downloadFile")
//}
