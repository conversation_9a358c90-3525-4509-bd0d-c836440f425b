<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">
    <style name="Launcher" parent="android:Theme.DeviceDefault.NoActionBar.Fullscreen">
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
        <item name="android:windowBackground">@drawable/splash</item>
        <item name="android:windowFullscreen">true</item>
    </style>
    <style name="Theme.AIH_User" parent="android:Theme.Material.Light.NoActionBar">
        <item name="android:windowLayoutInDisplayCutoutMode" tools:targetApi="o_mr1">shortEdges</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
    </style>
</resources>