<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="120dp"
    android:layout_height="180dp"
    android:background="@android:color/transparent">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardViewContainer"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp">
        <!-- A bit more elevation for better visual separation -->

        <!-- Camera Preview -->
        <androidx.camera.view.PreviewView
            android:id="@+id/previewView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:implementationMode="compatible"
            app:scaleType="fillCenter" />
        <!-- fill<PERSON>enter usually works well for previews -->

        <!-- Head Scope Indicator - shows when no face is detected -->
        <ImageView
            android:id="@+id/headScopeImageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:scaleType="fitCenter"
            android:src="@drawable/icon_head_scope"
            android:visibility="gone" />
            
        <!-- Body Indicator - shows when no body is detected -->
        <ImageView
            android:id="@+id/bodyIndicatorImageView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:scaleType="fitCenter"
            android:src="@drawable/video_scan_aiback_2"
            android:visibility="gone" />

        <!-- Mask Container: This FrameLayout will hold the background image and the button -->
        <FrameLayout
            android:id="@+id/maskView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone">
            <!-- Initially hidden, controlled by FloatingCameraService -->
            <!-- Adjust scaleType as needed for your image -->
            <ImageView
                android:id="@+id/maskBackgroundImageView"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:importantForAccessibility="no"
                android:scaleType="fitXY"
                android:adjustViewBounds="false"
                android:src="@drawable/float_window_background_for_aineck" />

            <Button
                android:id="@+id/togglePreviewButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:minHeight="36dp"
                android:paddingStart="12dp"
                android:paddingEnd="12dp"
                android:text="@string/open_video"
                android:textSize="12sp" />
        </FrameLayout>

        <!-- Analysis Text (Angle display) -->
        <TextView
            android:id="@+id/analysisImageView"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="bottom|center_horizontal"
            android:layout_marginBottom="8dp"
            android:background="#66000000"
            android:padding="5dp"
            android:text="@string/floating_camera_angle_text_placeholder"
            android:textColor="@android:color/white"
            android:textSize="14sp" />

        <!-- Close Button for the entire floating window -->
        <ImageView
            android:id="@+id/closeView"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="top|end"
            android:layout_margin="6dp"
            android:contentDescription="@string/close_button_description"
            android:src="@drawable/icon_window_close" />

    </androidx.cardview.widget.CardView>
</FrameLayout>