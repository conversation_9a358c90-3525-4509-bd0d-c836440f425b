<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="125dp"
    android:height="125dp"
    android:viewportWidth="125"
    android:viewportHeight="125">
  <path
      android:pathData="M44.03,38.55C50.43,33.53 78.35,7.68 106.85,29.9C135.34,52.13 130.58,108.71 88.72,121.49C46.86,134.28 7.84,102.3 0.77,71.25C-6.31,40.2 37.63,43.57 44.03,38.55Z"
      android:fillColor="#C3DFFF"
      android:fillType="evenOdd" />
  <path
      android:pathData="M88.72,121.49C73.91,126.01 59.46,124.93 46.65,120.28C47.74,118.31 49.35,116.77 51.48,115.66C53.86,114.42 56.87,113.72 60.5,113.59C60.23,108.63 61.51,104.97 64.33,102.59C67.15,100.21 71.29,99.31 76.74,99.9C77.7,94.12 80.08,90.29 83.86,88.4C87.65,86.51 92.36,86.81 98,89.3C100.47,85.83 103.15,83.91 106.03,83.53C108.24,83.24 110.45,84.17 112.08,85.15L112.46,85.39L112.82,85.62L113.15,85.85L113.45,86.07C113.5,86.11 113.55,86.14 113.59,86.18L113.97,86.47L114.36,86.8L114.68,87.09C116.65,83.13 119.71,81.14 123.84,81.11C120.51,98.92 108.94,115.31 88.72,121.49Z"
      android:fillColor="#E6F1FF"
      android:fillType="evenOdd" />
  <path
      android:pathData="M82.87,78.09L112.74,105.51C106.89,112.68 98.9,118.38 88.72,121.5C82.79,123.31 76.91,124.22 71.18,124.36L60.5,91.79L70.17,81.97L82.87,78.09Z"
      android:strokeAlpha="0.7"
      android:fillColor="#7CB9FF"
      android:fillType="evenOdd"
      android:fillAlpha="0.7" />
  <path
      android:pathData="M33.45,113.59C38.97,113.59 43.45,109.11 43.45,103.59C43.45,98.06 38.97,93.59 33.45,93.59C27.92,93.59 23.45,98.06 23.45,103.59C23.45,109.11 27.92,113.59 33.45,113.59Z"
      android:strokeAlpha="0.982329"
      android:fillAlpha="0.982329">
    <aapt:attr name="android:fillColor">
      <gradient
          android:centerX="504.29"
          android:centerY="1093.59"
          android:gradientRadius="1323.39"
          android:type="radial">
        <item
            android:offset="0"
            android:color="#FFFFFFFF" />
        <item
            android:offset="1"
            android:color="#FF75D5FE" />
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M33.45,113.59C38.97,113.59 43.45,109.11 43.45,103.59C43.45,98.06 38.97,93.59 33.45,93.59C27.92,93.59 23.45,98.06 23.45,103.59C23.45,109.11 27.92,113.59 33.45,113.59Z"
      android:strokeAlpha="0.982329"
      android:fillColor="#000000"
      android:fillAlpha="0.982329" />
  <path
      android:pathData="M105.45,54.59C108.76,54.59 111.45,51.9 111.45,48.59C111.45,45.27 108.76,42.59 105.45,42.59C102.13,42.59 99.45,45.27 99.45,48.59C99.45,51.9 102.13,54.59 105.45,54.59Z"
      android:strokeAlpha="0.7"
      android:fillAlpha="0.7">
    <aapt:attr name="android:fillColor">
      <gradient
          android:centerX="387.95"
          android:centerY="642.59"
          android:gradientRadius="794.03"
          android:type="radial">
        <item
            android:offset="0"
            android:color="#FFFFFFFF" />
        <item
            android:offset="1"
            android:color="#FF75D5FE" />
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M105.45,54.59C108.76,54.59 111.45,51.9 111.45,48.59C111.45,45.27 108.76,42.59 105.45,42.59C102.13,42.59 99.45,45.27 99.45,48.59C99.45,51.9 102.13,54.59 105.45,54.59Z"
      android:strokeAlpha="0.7"
      android:fillColor="#000000"
      android:fillAlpha="0.7" />
  <path
      android:pathData="M108.45,69.59C110.11,69.59 111.45,68.24 111.45,66.59C111.45,64.93 110.11,63.59 108.45,63.59C106.79,63.59 105.45,64.93 105.45,66.59C105.45,68.24 106.79,69.59 108.45,69.59Z"
      android:strokeAlpha="0.5"
      android:fillColor="#71B3FF"
      android:fillAlpha="0.5" />
  <path
      android:pathData="M108.45,69.59C110.11,69.59 111.45,68.24 111.45,66.59C111.45,64.93 110.11,63.59 108.45,63.59C106.79,63.59 105.45,64.93 105.45,66.59C105.45,68.24 106.79,69.59 108.45,69.59Z"
      android:strokeAlpha="0.5"
      android:fillColor="#000000"
      android:fillAlpha="0.5" />
  <path
      android:pathData="M24.95,90.59C26.88,90.59 28.45,89.02 28.45,87.09C28.45,85.15 26.88,83.59 24.95,83.59C23.01,83.59 21.45,85.15 21.45,87.09C21.45,89.02 23.01,90.59 24.95,90.59Z"
      android:strokeAlpha="0.5"
      android:fillColor="#71B3FF"
      android:fillAlpha="0.5" />
  <path
      android:pathData="M24.95,90.59C26.88,90.59 28.45,89.02 28.45,87.09C28.45,85.15 26.88,83.59 24.95,83.59C23.01,83.59 21.45,85.15 21.45,87.09C21.45,89.02 23.01,90.59 24.95,90.59Z"
      android:strokeAlpha="0.5"
      android:fillColor="#000000"
      android:fillAlpha="0.5" />
  <path
      android:pathData="M46.29,79.98V89.22L54.32,103.59L59.1,89.22L46.29,79.98Z"
      android:fillColor="#0B0C0C"
      android:fillType="evenOdd" />
  <path
      android:pathData="M79.37,60.7L87.34,63.59L95.74,78.14L80.88,74.84L79.37,60.7Z"
      android:fillColor="#0F1010"
      android:fillType="evenOdd" />
  <path
      android:pathData="M22.93,0.81C33.69,-3.78 75.42,9.74 82.87,78.09C68.57,81.73 61.04,91.51 61.04,91.51C5.66,57.55 12.16,5.39 22.93,0.81Z"
      android:fillColor="#71B3FF" />
  <path
      android:pathData="M22.93,0.81C33.69,-3.78 75.42,9.74 82.87,78.09C68.57,81.73 61.04,91.51 61.04,91.51C5.66,57.55 12.16,5.39 22.93,0.81Z"
      android:fillColor="#71B3FF" />
  <path android:pathData="M71.73,36.23L29.63,62.84C23.44,54.03 19.65,45.16 17.61,36.97L55.59,13.44C61.38,18.95 67.02,26.4 71.73,36.23Z">
    <aapt:attr name="android:fillColor">
      <gradient
          android:startX="4080.49"
          android:startY="863.23"
          android:endX="143.84"
          android:endY="2747.45"
          android:type="linear">
        <item
            android:offset="0"
            android:color="#FFBCDBFF" />
        <item
            android:offset="1"
            android:color="#FFB7D9FF" />
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M71.73,36.23L29.63,62.84C23.44,54.03 19.65,45.16 17.61,36.97L55.59,13.44C61.38,18.95 67.02,26.4 71.73,36.23Z"
      android:fillColor="#71B3FF" />
  <path
      android:pathData="M43.45,47.09C48.97,47.09 53.45,42.61 53.45,37.09C53.45,31.56 48.97,27.09 43.45,27.09C37.93,27.09 33.45,31.56 33.45,37.09C33.45,42.61 37.93,47.09 43.45,47.09Z"
      android:fillColor="#71B3FF" />
  <path
      android:pathData="M43.45,47.09C48.97,47.09 53.45,42.61 53.45,37.09C53.45,31.56 48.97,27.09 43.45,27.09C37.93,27.09 33.45,31.56 33.45,37.09C33.45,42.61 37.93,47.09 43.45,47.09Z"
      android:fillColor="#C4DFFF" />
  <path
      android:pathData="M31.61,0.34L17.61,8.23C18.95,4.31 20.86,1.69 22.92,0.81C24.91,-0.04 27.94,-0.27 31.61,0.34Z"
      android:fillColor="#444D56"
      android:fillType="evenOdd" />
  <path
      android:pathData="M77.92,79.62L63.21,69.69C63.12,69.63 62.99,69.65 62.93,69.74C62.89,69.81 62.88,69.89 62.93,69.96L71,83.21L77.92,79.62Z"
      android:fillColor="#111212"
      android:fillType="evenOdd" />
  <path
      android:pathData="M62.7,69.58C62.91,69.45 63.17,69.5 63.33,69.67L63.39,69.74L75.97,90.02C76.12,90.26 76.04,90.57 75.81,90.71C75.6,90.84 75.33,90.8 75.18,90.62L75.12,90.55L62.54,70.27C62.39,70.04 62.46,69.73 62.7,69.58Z"
      android:fillColor="#667584"
      android:fillType="evenOdd" />
</vector>
