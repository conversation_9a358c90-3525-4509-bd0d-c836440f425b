<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="139dp"
    android:height="117dp"
    android:viewportWidth="139"
    android:viewportHeight="117">
    <path
        android:pathData="M57.25,20.32C66.71,15.39 89.25,-12.46 121.07,10.94C152.88,34.33 135.24,77.76 116.92,99.31C98.6,120.85 60.6,122.92 30.11,102.52C-0.37,82.12 -2.58,66.35 3.1,50.11C8.78,33.86 20.1,27.45 57.25,20.32Z"
        android:fillColor="#C3DFFF"
        android:fillType="evenOdd" />
    <path
        android:pathData="M114.94,90.75C128.41,74.66 135.15,66.61 135.15,66.61C138.42,58.28 139.49,48.21 138.35,36.41L122.17,13.35L114.94,90.75Z"
        android:fillColor="#72B3FF"
        android:fillType="evenOdd" />
    <path
        android:pathData="M117.57,86.87L124.7,36.04C124.91,34.56 123.88,33.19 122.4,32.99C122.28,32.97 122.15,32.96 122.03,32.96H68.76L58.92,90.74H113.12C115.36,90.74 117.26,89.09 117.57,86.87Z"
        android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="6639.81"
                android:startY="2922.01"
                android:endX="58.92"
                android:endY="2922.01"
                android:type="linear">
                <item
                    android:offset="0"
                    android:color="#FFA1CCFF" />
                <item
                    android:offset="1"
                    android:color="#FF9BCAFF" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M115.77,85.97L122.9,35.14C123.11,33.66 122.08,32.3 120.6,32.09C120.48,32.07 120.35,32.06 120.22,32.06H66.96L57.12,89.84H111.32C113.56,89.84 115.46,88.19 115.77,85.97Z"
        android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="6094.3"
                android:startY="3230.49"
                android:endX="735.26"
                android:endY="826.52"
                android:type="linear">
                <item
                    android:offset="0"
                    android:color="#FFA0BACE" />
                <item
                    android:offset="1"
                    android:color="#FFC5E6FF" />
            </gradient>
        </aapt:attr>
    </path>
    <path android:pathData="M131.25,9.19C117.2,6.19 103.15,3.19 89.09,0.2C84.99,-0.68 80.83,1.4 79.06,5.2L66.12,32.96L112.37,79.39C113.33,61.26 114.88,48.08 117.03,39.85C120.25,27.51 123.66,14.56 131.25,9.19Z">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="6050.13"
                android:startY="0"
                android:endX="1436.2"
                android:endY="6522.65"
                android:type="linear">
                <item
                    android:offset="0"
                    android:color="#FFFFF2F2" />
                <item
                    android:offset="1"
                    android:color="#FFFEE4C0" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M131.25,9.19C117.2,6.19 103.15,3.19 89.09,0.2C84.99,-0.68 80.83,1.4 79.06,5.2L66.12,32.96L112.37,79.39C113.33,61.26 114.88,48.08 117.03,39.85C120.25,27.51 123.66,14.56 131.25,9.19Z"
        android:fillColor="#000000" />
    <path
        android:pathData="M76.72,78.93L114.94,74.93L109.82,24.56C109.64,22.72 108.09,21.32 106.24,21.32H77.87L72.76,75.01C72.57,76.99 74.03,78.75 76.01,78.94C76.24,78.96 76.48,78.96 76.72,78.93Z"
        android:fillType="evenOdd">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="4292.53"
                android:startY="2902.97"
                android:endX="72.75"
                android:endY="2902.97"
                android:type="linear">
                <item
                    android:offset="0"
                    android:color="#FF0C7CFF" />
                <item
                    android:offset="1"
                    android:color="#FF5FBCFF" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M21.18,19.38L27.34,83.47C27.77,87.89 31.47,91.25 35.9,91.25H112.62C114.61,91.25 116.22,89.64 116.22,87.65C116.22,87.53 116.21,87.41 116.2,87.29L109.82,24.56C109.64,22.72 108.09,21.32 106.24,21.32H48.16L45.02,16.94C44.34,16 43.25,15.44 42.09,15.44H24.76C22.77,15.44 21.16,17.05 21.16,19.04C21.16,19.15 21.17,19.27 21.18,19.38Z"
        android:strokeAlpha="0.527507"
        android:fillType="evenOdd"
        android:fillAlpha="0.527507">
        <aapt:attr name="android:fillColor">
            <gradient
                android:startX="9526.75"
                android:startY="3806.16"
                android:endX="21.16"
                android:endY="3806.16"
                android:type="linear">
                <item
                    android:offset="0"
                    android:color="#FF0277FF" />
                <item
                    android:offset="1"
                    android:color="#FFFFFFFF" />
            </gradient>
        </aapt:attr>
    </path>
    <path
        android:pathData="M19.38,20.28L25.53,83.48C25.96,87.89 29.67,91.25 34.09,91.25H110.81C112.8,91.25 114.41,89.64 114.41,87.65C114.41,87.53 114.4,87.4 114.39,87.28L108.03,26.35C107.84,24.51 106.29,23.12 104.45,23.12H46.36L43.2,18.03C42.54,16.98 41.39,16.34 40.14,16.34H22.97C20.98,16.34 19.37,17.95 19.37,19.94C19.37,20.05 19.37,20.17 19.38,20.28Z"
        android:fillColor="#71B3FF"
        android:fillType="evenOdd" />
    <path
        android:pathData="M64.26,47.89H39.06C36.58,47.89 34.56,49.91 34.56,52.39C34.56,54.88 36.58,56.89 39.06,56.89H64.26C66.75,56.89 68.76,54.88 68.76,52.39C68.76,49.91 66.75,47.89 64.26,47.89Z"
        android:strokeAlpha="0.8"
        android:fillColor="#FFFAF4"
        android:fillAlpha="0.8" />
    <path
        android:pathData="M52.56,66.79H39.06C36.58,66.79 34.56,68.8 34.56,71.29C34.56,73.77 36.58,75.79 39.06,75.79H52.56C55.05,75.79 57.06,73.77 57.06,71.29C57.06,68.8 55.05,66.79 52.56,66.79Z"
        android:strokeAlpha="0.8"
        android:fillColor="#FFFAF4"
        android:fillAlpha="0.8" />
</vector>
