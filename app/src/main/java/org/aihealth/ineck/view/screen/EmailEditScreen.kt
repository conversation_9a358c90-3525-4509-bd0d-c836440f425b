package org.aihealth.ineck.view.screen

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.BasicTextField
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import org.aihealth.ineck.R
import org.aihealth.ineck.ui.theme.AIH_UserTheme
import org.aihealth.ineck.util.DialogUtil
import org.aihealth.ineck.util.finish
import org.aihealth.ineck.util.localeResources
import org.aihealth.ineck.viewmodel.PersonalDataViewModel
import org.aihealth.ineck.viewmodel.user
import java.util.regex.Pattern

@Composable
fun EmailEditRoute(
    viewModel: PersonalDataViewModel = viewModel()
) {
    val currentEmail = user.email

    EmailEditScreen(
        initialEmail = currentEmail,
        onSave = { email ->
            if (email.isBlank()) {
                // 允许清空邮箱
                viewModel.setEmail("")
                finish()
            } else if (isValidEmail(email)) {
                viewModel.setEmail(email)
                finish()
            } else {
                DialogUtil.showToast(localeResources.getString(R.string.email_account_format_error))
            }
        },
        onCancel = { finish() }
    )
}

@Composable
fun EmailEditScreen(
    initialEmail: String = "",
    onSave: (String) -> Unit = { },
    onCancel: () -> Unit = {}
) {
    var email by remember { mutableStateOf(initialEmail) }

    BaseEditScreen(
        onSave = {
            onSave(email.trim())
        },
        onCancel = onCancel
    ) {
        // Email Field Container
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(
                    color = Color.White,
                    shape = RoundedCornerShape(8.dp)
                )
                .padding(16.dp)
        ) {
            EmailField(
                label = stringResource(R.string.e_mail),
                value = email,
                onValueChange = { email = it }
            )
        }
    }
}

@Composable
private fun EmailField(
    label: String,
    value: String,
    onValueChange: (String) -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            fontSize = 16.sp,
            color = Color(0xFF666666),
            fontWeight = FontWeight.Normal,
            modifier = Modifier.width(100.dp)
        )

        BasicTextField(
            value = value,
            onValueChange = { newValue ->
                // Limit to reasonable email length and basic filtering
                if (newValue.length <= 100 && !newValue.contains("\n")) {
                    onValueChange(newValue)
                }
            },
            modifier = Modifier.weight(1f),
            decorationBox = { innerTextField ->
                Box(
                    modifier = Modifier.fillMaxWidth(),
                    contentAlignment = Alignment.CenterEnd
                ) {
                    if (value.isEmpty()) {
                        Text(
                            text = "",
                            fontSize = 16.sp,
                            color = Color(0xFFC7C7CC)
                        )
                    }
                    innerTextField()
                }
            },
            textStyle = androidx.compose.ui.text.TextStyle(
                fontSize = 16.sp,
                color = Color(0xFF333333),
                textAlign = TextAlign.End
            ),
            singleLine = true,
            keyboardOptions = KeyboardOptions(
                keyboardType = KeyboardType.Email
            )
        )
    }
}

private fun isValidEmail(email: String): Boolean {
    val emailPattern = Pattern.compile(
        "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    )
    return emailPattern.matcher(email).matches()
}

@Preview(showSystemUi = true, showBackground = true)
@Composable
private fun PreviewEmailEditScreen() {
    AIH_UserTheme {
        EmailEditScreen()
    }
}
