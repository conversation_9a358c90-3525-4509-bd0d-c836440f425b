package org.aihealth.ineck


sealed class Screen(val route: String) {

    data object Splash : Screen("splash")

    /**
     * 首次登录四张图片界面
     */
    data object OnBoarding : Screen("on_boarding")

    /**
     * 更改城市界面
     */
    data object ChangeCity : Screen("change_city")

    /**
     * 登录父界面
     */
    data object Login : Screen("login")

    data object ForgetPassword: Screen("forget_password")

    /**
     * 英文注册界面
     */
    data object LoginSignUpEN : Screen("login_en_signup")

    /**
     * 中国服务器注册页
     */
    data object LoginSignUpChina : Screen("login_china_signup")

    /**
     * 首次登录修改资料界面
     */
    data object FirstUpdateData : Screen("first_update_data")

    /**
     * 登录成功后主界面
     */
    data object Main : Screen("main")

    /**
     * 隐私条款界面
     */
    data object PrivateTerm : Screen("private_term")

    /**
     * 免责申明界面
     */
    data object Disclaimer : Screen("disclaimer")

    /**
     * 用户协议
     */
    data object UserAgreement : Screen("user_agreement")

    /**
     * 会员协议
     */
    data object MemberAgreement : Screen("member_agreement")


    /**
     * 设备设置界面
     */
    data object DeviceSettings : Screen("device_settings")

    /**
     * 个人资料界面
     */
    data object PersonalData : Screen("personal_data")

    /**
     * 版本信息界面
     */
    data object VersionInformation : Screen("version_information")

    /**
     * 功能介绍界面
     */
    data object FunctionIntroduction : Screen("function_introduction")

    /**
     * 帮助和反馈界面
     */
    data object HelpFeedback : Screen("help_feedback")

    /**
     * 意见反馈界面
     */
    data object Feedback : Screen("feedback")

    /**
     * 设备匹配界面
     */
    data object MatchingDevice : Screen("matching_device")

    /**
     * 模式选择界面
     */
    data object FormatDevice : Screen("format_device")

    /**
     * 设备校准界面
     */
    data object CalibrationDevice : Screen("calibration_device")
    data object CalibrationDeviceRoute : Screen("calibration_device_route")

    /**
     * 健康贴士界面
     */
    data object HealthTip : Screen("health_tip")

    /**
     * 疼痛记录表单界面
     */
    data object PainRecord : Screen("pain_record")

    /**
     * ODI、Promis记录表单界面
     */
    data object OdiPromisRecord : Screen("odi_promis_record")

    /**
     * ODI 历史记录表单界面
     */
    data object OdiHisRecord : Screen("odi_his_record")

    /**
     * ODI 历史记录表单界面
     */
    data object PromisHisRecord : Screen("promis_his_record")

    /**
     * 我的关注界面
     */
    data object MyAttention : Screen("my_attention")

    /**
     * 添加关注界面
     */
    data object AddAttention : Screen("add_attention")

    /**
     * 聊天界面
     */
    data object Chat : Screen("chat")

    /**
     * 历史量表记录界面
     */
    data object HistoryIndexStatus : Screen("history_index_status")

    /**
     *  生命体征历史记录页面
     */
    data object HistoryVitalSigns : Screen("history_vital_signs")

    /**
     *  疼痛历史记录页面
     */
    data object HistoryPain : Screen("history_pain")

    /**
     *  改善方案练习详情页
     */
    data object ImprovementDetail : Screen(route = "improvement_detail")

    /**
     * 注销账号界面
     */
    data object LogOff : Screen("logoff")

    /**
     * 注销账户成功界面
     */
    data object LogOffSuccess : Screen("logoff_success")

    data object LogOffRequireScreen : Screen("logoff_require_screen")

    // 测试界面
    data object Test : Screen("test")

    // 视频播放
    data object Training : Screen(route = "training")

    /**
     * 跟练评价
     */
    data object TrainingEvaluation : Screen(route = "training_evaluation")

    // 会员中心
    data object MemberShipCenter : Screen(route = "membership_center")

    // 兑换页
    data object UseRedemptionCodeScreen : Screen(route = "use_redemption_code_screen")

    // 支付收银台
    data object PaymentScreen : Screen(route = "payment_screen")

    // 评价页
    data object EvaluationScreen : Screen(route = "evaluation_screen")


    /**
     * 隐私和安全
     */
    data object PrivacyAndSecurity : Screen(route = "privacy_and_security")

    /**
     * 问卷欢迎页
     */
    data object QuestionnaireWelcome : Screen(route = "questionnaire_welcome")

    /**
     * aiback问卷欢迎页
     */
    data object AiBackQuestionnaireWelcome : Screen(route = "ai_back_questionnaire_welcome")


    /**
     * 问卷入口
     */
    data object QuestionnaireGuide : Screen(route = "questionnaire_guide")

    /**
     * ai back 问卷入口
     */
    data object AiBackQuestionnaireGuide : Screen(route = "ai_back_questionnaire_guide")


    /**
     * 问卷报告页
     */
    data object QuestionnaireReport : Screen(route = "questionnaire_report")


    data object ThirdPartyDataSources : Screen(route = "third_party_data_sources")


    data object QuestionnaireResultScreen : Screen(route = "questionnaire_result_Screen")

    data object QuestionnaireReportResultScreen :
        Screen(route = "questionnaire_report_result_Screen")

    /**
     * 关于我们
     */
    data object MyAbout : Screen(route = "my_about")

    /**
     * 通知中心
     */
    data object MessageCenter : Screen(route = "message_center")

    data object LanguageSettings : Screen(route = "language_settings")

    /**
     * aiJoint 集合页
     */
    data object AiJoint : Screen(route = "ai_joint")

    data object KneeExerciseScreen : Screen(route = "knee_exercise_screen")

    data object KneeExerciseReportScreen : Screen(route = "knee_exercise_report_screen")

    data object NeuralScaleScreen : Screen(route = "neural_scale_screen")

    data object UnitSettings : Screen(route = "unit_settings")

    /**
     * 设备连接界面
     */
    data object DeviceConnect : Screen(route = "device_connect")

    data object QuestionnaireReportScreen : Screen(route = "questionnaire_report_screen")

    data object AiBackQuestionnaireReportScreen :
        Screen(route = "questionnaire_report_screen_ai_back")

    /**
     * Zoom Meeting
     */
    data object MeetingScreen : Screen(route = "meeting_screen")

    /**
     * 姓名编辑页面
     */
    data object NameEdit : Screen(route = "name_edit")
    
    /**
     * 性别编辑页面
     */
    data object GenderEdit : Screen(route = "gender_edit")

    /**
     * 出生日期编辑页面
     */
    data object BirthdateEdit : Screen(route = "birthdate_edit")

    /**
     * 邮箱编辑页面
     */
    data object EmailEdit : Screen(route = "email_edit")
}