# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.kts.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# 需要Gson数据类方法名
-keepclassmembers class org.aihealth.ineck.model.angles.* {*;}
-keepclassmembers class org.aihealth.ineck.model.vitalsigns.* {*;}
-keepclassmembers class org.aihealth.ineck.model.improvement.* {*;}
-keepclassmembers class org.aihealth.ineck.model.IdModel {*;}

# ----------------------------------------- 第三方库、框架、SDK ---------------------------------------

##---------------Begin: proguard configuration for Gson  ----------
# Gson uses generic type information stored in a class file when working with fields. Proguard
# removes such information by default, so configure it to keep all of it.
-keepattributes Signature

# For using GSON @Expose annotation
-keepattributes *Annotation*

# Gson specific classes
-dontwarn sun.misc.**
#-keep class com.google.gson.stream.** { *; }

# Application classes that will be serialized/deserialized over Gson
-keep class com.google.gson.examples.android.model.** { <fields>; }

# Prevent proguard from stripping interface information from TypeAdapter, TypeAdapterFactory,
# JsonSerializer, JsonDeserializer instances (so they can be used in @JsonAdapter)
-keep class * implements com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Prevent R8 from leaving Data object members always null
-keepclassmembers,allowobfuscation class * {
  @com.google.gson.annotations.SerializedName <fields>;
}


# With R8 full mode, it sees no subtypes of Retrofit interfaces since they are created with a Proxy
# and replaces all potential values with null. Explicitly keeping the interfaces prevents this.
-if interface * { @retrofit2.http.* <methods>; }
-keep,allowobfuscation interface <1>

# OkHttp3

-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**
# okio
#-dontwarn okio.**
#-keep class okio.**{*;}
#-keep interface okio.**{*;}

# Retrofit
# -dontwarn retrofit2.**
# -keep class retrofit2.** { *; }
# -keepattributes Signature
# -keepattributes Exceptions
# -keep public class * extends retrofit2.Converter {*;}

#Google
-keep class com.google.android.gms.** { *; }

# ------------------------- 升级到 Gradle8.0 之后的release编译需要 ------------------------------
# Please add these rules to your existing keep rules in order to suppress warnings.
# This is generated automatically by the Android Gradle plugin.
-dontwarn com.alipay.sdk.app.**
-dontwarn com.ss.android.larksso.**
-dontwarn com.tencent.wework.api.**

-keep class no.nordicsemi.android.dfu.** { *; }
-keep class com.hjq.permissions.** {*;}

-keep class us.zoom**{
   *;
}
-keep interface us.zoom**{
   *;
}

-keep class org.webrtc**{
   *;
}

-keep class com.zipow**{
   *;
}