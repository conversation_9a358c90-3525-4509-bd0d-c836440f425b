{"formatVersion": 1, "database": {"version": 1, "identityHash": "9a5afa34f6a98338ff0ca92baa57a961", "entities": [{"tableName": "<PERSON><PERSON>", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `uuid` TEXT NOT NULL, `angle` INTEGER NOT NULL, `type` TEXT NOT NULL, `timestamp` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "uuid", "columnName": "uuid", "affinity": "TEXT", "notNull": true}, {"fieldPath": "angle", "columnName": "angle", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_Angle_timestamp", "unique": true, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_Angle_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}], "foreignKeys": []}, {"tableName": "angleCV", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` TEXT NOT NULL, `angle` INTEGER NOT NULL, `timestamp` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "TEXT", "notNull": true}, {"fieldPath": "angle", "columnName": "angle", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "timestamp", "columnName": "timestamp", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"columnNames": ["id"], "autoGenerate": true}, "indices": [{"name": "index_angleCV_timestamp", "unique": true, "columnNames": ["timestamp"], "orders": [], "createSql": "CREATE UNIQUE INDEX IF NOT EXISTS `index_angleCV_timestamp` ON `${TABLE_NAME}` (`timestamp`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '9a5afa34f6a98338ff0ca92baa57a961')"]}}