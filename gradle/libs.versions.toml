[versions]
# package

hbrecorderVersion = "3.0.9"
minSdk = "26"
compileSdk = "36"
targetSdk = "36"

agpVersion = "8.10.1"
kpsVersion = "2.2.0-2.0.2"
kotlinVersion = "2.2.0"
composeBomVersion = "2025.07.00"
navVersion = "2.9.3"
activityComposeVersion = "1.10.1"
coreKtxVersion = "1.16.0"
lifecycleRuntimeKtxVersion = "2.9.2"
threetenabpVersion = "1.4.9"
visionCommonVersion = "17.3.0"
workRuntimeKtxVersion = "2.10.3"
accompanistVersion = "0.36.0"
foundationVersion = "1.8.3"
coilComposeVersion = "2.7.0"
constraintLayoutComposeVersion = "1.1.1"
retrofitVersion = "3.0.0"
gsonVersion = "2.13.1"
loggingInterceptorVersion = "5.1.0"
authingVersion = "3.1.18"
auth0Version = "3.8.0"
timberVersion = "5.0.1"
wechatVersion = "6.8.34"
wechatPayVersion = "0.2.17"
scanVersion = "3.5.3"
cameraVersion = "1.4.2"
roomVersion = "2.7.2"
lottieVersion = "6.6.7"
faceDetectionVersion = "16.1.7"
mediaPipeVersion = "0.10.26"
googleLoginVersion = "21.4.0"
nordicsemiVersion = "2.9.0"
facebookVersion = "18.1.3"
media3Version = "1.8.0"
cronetVersion = "119.6045.31"
connectClientVersion = "1.1.0-rc03"
jwtdecodeVersion = "2.0.2"
hiltVersion = "2.57"
hiltNavigationComposeVersion = "1.2.0"
# Test
espressoCoreVersion = "3.7.0"
junit = "1.3.0"
junitVersion = "4.13.2"

xxPermissionsVersion = "20.0"
libraryNoOpVersion = "4.2.0"
libraryVersion = "4.2.0"

billingVersion = "8.0.0"
firebaseBomVersion = "34.0.0"
installreferrerVersion = "2.2"
kotlinxSerializationJsonVersion = "1.9.0"
zoomvideosdkCoreVersion = "2.3.0"
runtimeAndroidVersion = "1.8.3"
accompanistSwiperefreshVersion = "0.36.0"
datastoreVersion = "1.1.7"
appcompatVersion = "1.7.1"
materialVersion = "1.12.0"
activityVersion = "1.10.1"
[libraries]

# Android
activity-compose = { module = "androidx.activity:activity-compose", version.ref = "activityComposeVersion" }
billing = { module = "com.android.billingclient:billing", version.ref = "billingVersion" }
billing-ktx = { module = "com.android.billingclient:billing-ktx", version.ref = "billingVersion" }
core-ktx = { module = "androidx.core:core-ktx", version.ref = "coreKtxVersion" }
firebase-analytics = { module = "com.google.firebase:firebase-analytics" }
firebase-bom = { module = "com.google.firebase:firebase-bom", version.ref = "firebaseBomVersion" }
firebase-crashlytics = { module = "com.google.firebase:firebase-crashlytics" }
firebase-crashlytics-ndk = { module = "com.google.firebase:firebase-crashlytics-ndk" }
firebase-messaging = { module = "com.google.firebase:firebase-messaging" }
firebase-pref = { module = "com.google.firebase:firebase-perf" }
hbrecorder = { module = "com.github.HBiSoft:HBRecorder", version.ref = "hbrecorderVersion" }
installreferrer = { module = "com.android.installreferrer:installreferrer", version.ref = "installreferrerVersion" }
kotlin-android-extensions-runtime = { module = "org.jetbrains.kotlin:kotlin-android-extensions-runtime", version.ref = "kotlinVersion" }
kotlin-parcelize-runtime = { module = "org.jetbrains.kotlin:kotlin-parcelize-runtime", version.ref = "kotlinVersion" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "kotlinxSerializationJsonVersion" }
lifecycle-runtime-ktx = { module = "androidx.lifecycle:lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtxVersion" }
lifecycle-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "lifecycleRuntimeKtxVersion" }
lifecycle-common = { module = "androidx.lifecycle:lifecycle-common", version.ref = "lifecycleRuntimeKtxVersion" }
lifecycle-viewmodel-ktx = { module = "androidx.lifecycle:lifecycle-viewmodel-ktx", version.ref = "lifecycleRuntimeKtxVersion" }
lifecycle-viewmodel-compose = { module = "androidx.lifecycle:lifecycle-viewmodel-compose", version.ref = "lifecycleRuntimeKtxVersion" }
lifecycle-service = { module = "androidx.lifecycle:lifecycle-service", version.ref = "lifecycleRuntimeKtxVersion" }
navigation-compose = { module = "androidx.navigation:navigation-compose", version.ref = "navVersion" }
threetenabp = { module = "com.jakewharton.threetenabp:threetenabp", version.ref = "threetenabpVersion" }
vision-common = { module = "com.google.mlkit:vision-common", version.ref = "visionCommonVersion" }
work-runtime-ktx = { module = "androidx.work:work-runtime-ktx", version.ref = "workRuntimeKtxVersion" }
# Compose
compose-bom = { module = "androidx.compose:compose-bom", version.ref = "composeBomVersion" }
compose-ui = { module = "androidx.compose.ui:ui" }
compose-ui-graphics = { module = "androidx.compose.ui:ui-graphics" }
compose-ui-tooling-preview = { module = "androidx.compose.ui:ui-tooling-preview" }
compose-material3 = { module = "androidx.compose.material3:material3" }
compose-ui-tooling = { module = "androidx.compose.ui:ui-tooling" }
compose-ui-test-junit = { module = "androidx.compose.ui:ui-test-junit4" }
compose-ui-test-manifest = { module = "androidx.compose.ui:ui-test-manifest" }
# Accompanist
accompanist-systemuicontroller = { module = "com.google.accompanist:accompanist-systemuicontroller", version.ref = "accompanistVersion" }
accompanist-pager = { module = "com.google.accompanist:accompanist-pager", version.ref = "accompanistVersion" }
accompanist-flowlayout = { module = "com.google.accompanist:accompanist-flowlayout", version.ref = "accompanistVersion" }
accompanist-navigation-material = { module = "com.google.accompanist:accompanist-navigation-material", version.ref = "accompanistVersion" }
accompanist-navigation-animation = { module = "com.google.accompanist:accompanist-navigation-animation", version.ref = "accompanistVersion" }
accompanist-webview = { module = "com.google.accompanist:accompanist-webview", version.ref = "accompanistVersion" }
# Compose Pager
compose-pager = { module = "androidx.compose.foundation:foundation", version.ref = "foundationVersion" }
# Coil
coil-compose = { module = "io.coil-kt:coil-compose", version.ref = "coilComposeVersion" }
# ConstraintLayout
constraintlayout-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "constraintLayoutComposeVersion" }
# Retrofit
retrofit = { module = "com.squareup.retrofit2:retrofit", version.ref = "retrofitVersion" }
retrofit-converter-gson = { module = "com.squareup.retrofit2:converter-gson", version.ref = "retrofitVersion" }
logging-interceptor = { module = "com.squareup.okhttp3:logging-interceptor", version.ref = "loggingInterceptorVersion" }
# Gson
gson = { module = "com.google.code.gson:gson", version.ref = "gsonVersion" }
# authing
authing-core = { module = "cn.authing:authing-java-sdk", version.ref = "authingVersion" }
# auth0
auth0 = { module = "com.auth0.android:auth0", version.ref = "auth0Version" }
# wechat
wechat = { module = "com.tencent.mm.opensdk:wechat-sdk-android", version.ref = "wechatVersion" }
wechat-pay = { module = "com.github.wechatpay-apiv3:wechatpay-java", version.ref = "wechatPayVersion" }
# QRCode
qrcode = { module = "com.google.zxing:core", version.ref = "scanVersion" }
# camera
camera = { module = "androidx.camera:camera-camera2", version.ref = "cameraVersion" }
camera-view = { module = "androidx.camera:camera-view", version.ref = "cameraVersion" }
camera-lifecycle = { module = "androidx.camera:camera-lifecycle", version.ref = "cameraVersion" }
# Room
room = { module = "androidx.room:room-runtime", version.ref = "roomVersion" }
room-ktx = { module = "androidx.room:room-ktx", version.ref = "roomVersion" }
room-compiler = { module = "androidx.room:room-compiler", version.ref = "roomVersion" }
# Datastore
datastore-preferences = { module = "androidx.datastore:datastore-preferences", version.ref = "datastoreVersion" }
datastore-preferences-core = { module = "androidx.datastore:datastore-preferences-core", version.ref = "datastoreVersion" }
# Lottie
lottie = { module = "com.airbnb.android:lottie-compose", version.ref = "lottieVersion" }
# MLKit
face-detection = { module = "com.google.mlkit:face-detection", version.ref = "faceDetectionVersion" }
# Pose Landmarker
pose-landmarker = { module = "com.google.mediapipe:tasks-vision", version.ref = "mediaPipeVersion" }
# Google Services Login
google-services-login = { module = "com.google.android.gms:play-services-auth", version.ref = "googleLoginVersion" }
# Nordic Semi
nordic-semi-dfu = { module = "no.nordicsemi.android:dfu", version.ref = "nordicsemiVersion" }
# Facebook SDK
facebook-sdk = { module = "com.facebook.android:facebook-android-sdk", version.ref = "facebookVersion" }
# Media3
media3-exoplayer = { module = "androidx.media3:media3-exoplayer", version.ref = "media3Version" }
media3-common = { module = "androidx.media3:media3-common", version.ref = "media3Version" }
media3-ui = { module = "androidx.media3:media3-ui", version.ref = "media3Version" }
media3-session = { module = "androidx.media3:media3-session", version.ref = "media3Version" }
media3-datasource-cronet = { module = "androidx.media3:media3-datasource-cronet", version.ref = "media3Version" }
# Cronet to Fix the crash in HuaWei
cronet-api = { module = "org.chromium.net:cronet-api", version.ref = "cronetVersion" }
cronet-fallback = { module = "org.chromium.net:cronet-fallback", version.ref = "cronetVersion" }
# Timber
timber = { module = "com.jakewharton.timber:timber", version.ref = "timberVersion" }
# Health connect google
connect-client = { module = "androidx.health.connect:connect-client", version.ref = "connectClientVersion" }
# Hilt
hilt-android = { module = "com.google.dagger:hilt-android", version.ref = "hiltVersion" }
hilt-compiler = { module = "com.google.dagger:hilt-compiler", version.ref = "hiltVersion" }
hilt-navigation-compose = { module = "androidx.hilt:hilt-navigation-compose", version.ref = "hiltNavigationComposeVersion" }
# JWT Decode
jwtdecode = { module = "com.auth0.android:jwtdecode", version.ref = "jwtdecodeVersion" }
# Testing
espresso-core = { module = "androidx.test.espresso:espresso-core", version.ref = "espressoCoreVersion" }
ext-junit = { module = "androidx.test.ext:junit", version.ref = "junit" }
junit = { module = "junit:junit", version.ref = "junitVersion" }
xxPermissions = { module = "com.github.getActivity:XXPermissions", version.ref = "xxPermissionsVersion" }
library-no-op = { group = "com.github.chuckerteam.chucker", name = "library-no-op", version.ref = "libraryNoOpVersion" }
library = { group = "com.github.chuckerteam.chucker", name = "library", version.ref = "libraryVersion" }
zoomvideosdk-core = { group = "us.zoom.videosdk", name = "zoomvideosdk-core", version.ref = "zoomvideosdkCoreVersion" }
runtime-android = { group = "androidx.compose.runtime", name = "runtime-android", version.ref = "runtimeAndroidVersion" }
accompanist-swiperefresh = { group = "com.google.accompanist", name = "accompanist-swiperefresh", version.ref = "accompanistSwiperefreshVersion" }
appcompat = { group = "androidx.appcompat", name = "appcompat", version.ref = "appcompatVersion" }
material = { group = "com.google.android.material", name = "material", version.ref = "materialVersion" }
activity = { group = "androidx.activity", name = "activity", version.ref = "activityVersion" }

[plugins]
android-library = { id = "com.android.library", version.ref = "agpVersion" }
android-application = { id = "com.android.application", version.ref = "agpVersion" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlinVersion" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlinVersion" }
devtools-ksp = { id = "com.google.devtools.ksp", version.ref = "kpsVersion" }
dagger-hilt = { id = "com.google.dagger.hilt.android", version.ref = "hiltVersion" }
compose-compiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlinVersion" }
[bundles]
compose = [
    "compose-ui", "compose-ui-graphics", "compose-ui-tooling-preview", "compose-material3"
]
media3 = [
    "media3-exoplayer", "media3-common", "media3-ui", "media3-session", "media3-datasource-cronet"
]
cronet = [
    "cronet-api", "cronet-fallback"
]