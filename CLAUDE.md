# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

AiH-User is a comprehensive Android health monitoring application built with Jetpack Compose. It provides AI-powered posture analysis, multi-device Bluetooth connectivity, and healthcare provider communication features. The app supports both Chinese and international markets with region-specific configurations.

## Build Commands

### Development
```bash
# Clean and build project
./gradlew clean assembleDebug

# Build release APK for Google Play
./gradlew assembleGooglePlayRelease

# Build release APK for China Store
./gradlew assembleChinaStoreRelease

# Build AAB bundle for Google Play
./gradlew bundleGooglePlayRelease

# Run tests
./gradlew test
./gradlew connectedAndroidTest

# Lint checking
./gradlew lint

# Generate baseline profiles
./gradlew generateBaselineProfile
```

### Device Variants
The app has two product flavors:
- `googlePlay` - International version with Google services
- `chinaStore` - China version with Umeng services and WeChat integration

## Architecture Overview

### Core Architecture Patterns
- **Single Activity Architecture** with Jetpack Compose navigation
- **MVVM Pattern** with ViewModels extending `BaseViewModel`
- **Repository Pattern** for data access abstraction
- **Clean Architecture** with clear separation of concerns

### Key Components

#### Navigation
- Uses Navigation Compose with type-safe routing via `Screen.kt` sealed classes
- Main routes defined in `Route.kt` and handled in `MainActivity.kt`
- Deep linking support for chat and follow functionality

#### Data Layer
- **Room Database**: `AppDatabase.kt` with entities for health data (`Angle`, `TimeStamp`, `StepEntity`)
- **Network Layer**: Retrofit-based API service with dual server configuration:
  - China: `https://aispine.aihnet.cn`
  - International: `https://myaih.net`
- **Local Storage**: DataStore for preferences, SharedPreferences via `SPUtil`

#### UI Layer
- **Jetpack Compose** with Material Design 3
- Custom components in `view/custom/` package
- Theme configuration in `ui/theme/`
- Responsive design with different layouts for various screen sizes

#### Business Logic
- ViewModels in `viewmodel/` package handle UI state and business logic
- Services in `service/` package for background operations
- Utilities in `util/` package for common functionality

### Key Feature Modules

#### Bluetooth & Device Integration (`bluetooth/`)
- BLE device scanning and connection management
- Support for aiNeck, aiBack, aiJoint hardware devices
- Real-time sensor data collection and processing
- Device firmware updates via Nordic DFU library

#### AI & Computer Vision (`mediapipe/`, `analyzer/`)
- MediaPipe integration for pose landmark detection
- Real-time joint angle calculation and tracking
- Video-based posture analysis with camera integration
- AI-powered exercise form validation

#### Health Data Management (`model/angles/`, `model/vitalsigns/`)
- Comprehensive health metrics tracking (posture, vital signs, pain levels)
- ODI and PROMIS questionnaire implementations
- Historical data analysis and trend visualization
- Data export functionality for healthcare providers

#### Communication (`network/`, `push/`)
- Real-time chat system with healthcare providers
- Video meeting integration using Zoom SDK
- Push notifications (FCM for international, Umeng for China)
- File sharing and medical document exchange

#### Payment & Membership (`googlepay/`, `model/`)
- Multi-platform payment integration (Google Pay, WeChat Pay)
- Subscription management with trial periods
- Regional payment method support

## Development Guidelines

### Code Organization
- Follow existing package structure with clear feature separation
- Use sealed classes for navigation and state management
- Implement proper error handling with `ApiException` and `ErrorMsgUtil`
- Maintain consistent naming conventions across ViewModels and components

### Testing
- Unit tests located in `src/test/`
- Instrumentation tests in `src/androidTest/`
- Mock implementations available in `mock/` package for development

### Localization
- String resources in `values/`, `values-en/`, `values-zh/`
- Region-specific configurations handled via product flavors
- Use `LauguageUtil` for runtime language switching

### Permissions & Security
- Bluetooth, camera, location permissions managed via `XXPermissions` library
- Secure keystore configuration for release builds
- Network security config in `xml/network_security_config.xml`
- Authentication via Auth0 (international) and Authing (China)

### Performance Considerations
- Baseline profiles configured for improved startup performance
- ProGuard configuration in `proguard-rules.pro`
- Memory management for video processing and Bluetooth operations
- Background service optimization for battery efficiency

## Common Development Tasks

### Adding New Health Metrics
1. Create data model in `model/` package
2. Add Room entity and DAO if persistence needed
3. Update API service in `network/ApiService.kt`
4. Create corresponding ViewModel
5. Implement UI components in `view/screen/`

### Bluetooth Device Integration
1. Define device characteristics in `bluetooth/DeviceUUID.kt`
2. Update `BluetoothConnection.kt` for device-specific communication
3. Add device type to `model/DeviceType.kt`
4. Implement data parsing in relevant analyzer classes

### Adding New Screens
1. Create screen identifier in `Screen.kt`
2. Add route handling in `Route.kt`
3. Implement Composable in `view/screen/`
4. Create corresponding ViewModel if needed
5. Update navigation calls from other screens

### Network API Integration
1. Add endpoints to `network/ApiService.kt`
2. Create request/response models in `model/` package
3. Implement repository methods if needed
4. Handle API calls in ViewModels with proper error handling

## Key Dependencies

### Core Android
- Jetpack Compose BOM 2025.05.01
- Navigation Compose 2.9.0
- Room 2.7.1
- Work Manager 2.10.1
- DataStore 1.1.7

### Networking & Authentication
- Retrofit 2.11.0
- Auth0 2.11.0 / Authing 3.1.10
- Socket.IO 2.1.0

### Health & AI
- MediaPipe 0.10.26
- ML Kit Face Detection 16.1.7
- CameraX 1.4.2

### Device Integration
- Nordic DFU 2.6.0
- XXPermissions 20.0

### Payment & Services
- Firebase BOM 33.14.0
- Google Play Billing 7.1.1
- WeChat SDK 6.8.30
- Zoom Video SDK 2.2.5

## Region-Specific Configurations

### China Version (`chinaStore`)
- Umeng push notifications and analytics
- WeChat login and payment integration
- Alternative server endpoints
- Specific launcher icons and app metadata

### International Version (`googlePlay`)
- Firebase services (FCM, Crashlytics, Analytics)
- Google Play services integration
- Auth0 authentication
- Google Pay billing

## Troubleshooting

### Common Build Issues
- Ensure proper keystore configuration in `keystore.properties`
- Check NDK configuration for ARM architectures
- Verify Google Services JSON files are in correct flavor directories

### Bluetooth Connectivity
- Bluetooth permissions must be granted at runtime
- Device pairing requires location services on Android 6+
- Check `BluetoothUtil.kt` for device-specific connection logic

### Video Processing Performance
- MediaPipe models are loaded from assets folder
- Camera resolution affects processing performance
- Use background threads for intensive computations